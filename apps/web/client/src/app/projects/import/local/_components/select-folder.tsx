import {
    ProcessedFileType,
    type NextJsProjectValidation,
    type ProcessedFile,
} from '@/app/projects/types';
import { IGNORED_UPLOAD_DIRECTORIES, IGNORED_UPLOAD_FILES } from '@onlook/constants';
import { Button } from '@onlook/ui/button';
import { CardDescription, CardTitle } from '@onlook/ui/card';
import { Icons } from '@onlook/ui/icons';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@onlook/ui/tabs';
import { isBinaryFile } from '@onlook/utility';
import { motion } from 'motion/react';
import { useCallback, useRef, useState, type HTMLAttributes } from 'react';
import { StepContent, StepFooter, StepHeader } from '../../steps';
import { useProjectCreation } from '../_context';

declare module 'react' {
    interface InputHTMLAttributes<T> extends HTMLAttributes<T> {
        webkitdirectory?: string;
        directory?: string;
    }
}

export const NewSelectFolder = () => {
    const {
        projectData,
        setProjectData,
        prevStep,
        nextStep,
        resetProjectData,
        validateNextJsProject,
    } = useProjectCreation();
    const [isDragging, setIsDragging] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const [error, setError] = useState('');
    const [validation, setValidation] = useState<NextJsProjectValidation | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [projectMode, setProjectMode] = useState<'upload' | 'local'>('upload');
    const [localPort, setLocalPort] = useState('3000');
    const [localProjectPath, setLocalProjectPath] = useState('');

    const extractProjectName = (files: ProcessedFile[]): string | null => {
        const packageJsonFile = files.find(
            (f) => f.path.endsWith('package.json') && f.type === ProcessedFileType.TEXT,
        );

        if (packageJsonFile) {
            try {
                const packageJson = JSON.parse(packageJsonFile.content as string);
                return packageJson.name || null;
            } catch (error) {
                console.warn('Error parsing package.json for name:', error);
            }
        }

        return null;
    };

    const handleClickUpload = () => {
        fileInputRef.current?.click();
    };
    
    const processLocalProjectFiles = useCallback(async (fileList: FileList | File[]) => {
        setError('');
        setIsUploading(true);

        try {
            const files = Array.from(fileList);
            const fullPath = files[0]?.webkitRelativePath;

            // Get just the folder path without processing files
            const normalizedPath = fullPath?.startsWith('/') ? fullPath.substring(1) : fullPath;
            const folderName = normalizedPath?.substring(0, normalizedPath.indexOf('/'));

            // Find package.json to get project name
            const packageJsonFile = files.find(f => f.name === 'package.json');
            let projectName = `Local Project (Port ${localPort})`;
            console.log('processLocalProjectFiles: Using port:', localPort);
            
            if (packageJsonFile) {
                try {
                    const content = await packageJsonFile.text();
                    const packageJson = JSON.parse(content);
                    projectName = packageJson.name || projectName;
                } catch (error) {
                    console.warn('Error parsing package.json:', error);
                }
            }

            // Auto-generate a likely absolute path if not already set
            if (!localProjectPath && folderName) {
                // Try to construct a reasonable default path based on common patterns
                let suggestedPath = '';
                
                // Detect operating system from user agent
                const isMac = navigator.userAgent.includes('Mac');
                const isWindows = navigator.userAgent.includes('Windows');
                
                if (isMac) {
                    // For macOS, provide a template that users can easily modify
                    suggestedPath = `/Users/<USER>/Documents/${folderName}`;
                } else if (isWindows) {
                    // For Windows
                    suggestedPath = `C:\\Users\\<USER>\\Documents\\${folderName}`;
                } else {
                    // For Linux/Unix
                    suggestedPath = `/home/<USER>/${folderName}`;
                }
                
                setLocalProjectPath(suggestedPath);
                console.log(`Auto-suggested project path based on folder "${folderName}": ${suggestedPath}`);
            }

            // Ensure we use the absolute path - localProjectPath should have been set above
            const absolutePath = localProjectPath; // This should be the template path that was auto-filled
            
            console.log(`processLocalProjectFiles: saving project with localPath: "${absolutePath}"`);
            
            setProjectData({
                name: projectName,
                folderPath: folderName || 'local-project', // Keep folderPath for display/compatibility
                localPath: absolutePath, // Use the actual absolute path for localPath
                files: [], // Don't store files for local projects
                isLocal: true,
                localPort: localPort,
            });

            // Skip validation for local projects
            setValidation({ isValid: true });
        } catch (error) {
            console.error('Error processing local project:', error);
            setError(error instanceof Error ? error.message : 'Failed to process local project');
        } finally {
            setIsUploading(false);
        }
    }, [localPort, localProjectPath]);
    
    const handleLocalContinue = useCallback(() => {
        if (projectMode === 'local' && localPort) {
            console.log('handleLocalContinue: Using port:', localPort, 'path:', localProjectPath);
            
            // Validate that the path doesn't contain placeholders
            if (localProjectPath.includes('[username]')) {
                setError('Please replace [username] with your actual username in the project path');
                return;
            }
            
            // Use the user-provided path or default to empty if not provided
            const projectPath = localProjectPath || '';
            const projectName = localProjectPath ? 
                `${localProjectPath.split('/').pop()} (Port ${localPort})` : 
                `Local Project (Port ${localPort})`;
            
            console.log('handleLocalContinue: Final project path being saved:', projectPath);
            
            // For local mode, create a minimal project with the user-provided path
            setProjectData({
                name: projectName,
                folderPath: 'local-project', // Keep folderPath for display/compatibility
                localPath: projectPath, // Use the actual absolute path for localPath
                files: [],
                isLocal: true,
                localPort: localPort,
            });
            
            // Skip validation for local projects
            setValidation({ isValid: true });
        }
    }, [projectMode, localPort, localProjectPath]);

    const filterAndProcessFiles = async (files: File[]): Promise<ProcessedFile[]> => {
        const processedFiles: ProcessedFile[] = [];
        const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

        // Find the common root path from all files
        const allPaths = files.map((file) => (file as any).webkitRelativePath || file.name);

        const rootPath =
            allPaths.length > 0 && allPaths[0].includes('/') ? allPaths[0].split('/')[0] : '';

        for (const file of files) {
            // Get relative path from webkitRelativePath or name
            // Remove the root path from the relative path
            let relativePath = (file as any).webkitRelativePath || file.name;
            relativePath = relativePath.replace(rootPath, '').replace(/^\//, '');

            // Skip ignored directories
            if (
                IGNORED_UPLOAD_DIRECTORIES.some(
                    (dir) => relativePath.includes(`${dir}/`) || relativePath.startsWith(`${dir}/`),
                )
            ) {
                continue;
            }

            // Skip ignored files
            if (IGNORED_UPLOAD_FILES.includes(file.name)) {
                continue;
            }

            let processedFile: ProcessedFile;

            // Skip if file is too large
            if (file.size > MAX_FILE_SIZE) {
                console.warn(`Skipping large file: ${file.name} (${file.size} bytes)`);
                continue;
            }

            // Determine if file is binary
            const type = isBinaryFile(file.name)
                ? ProcessedFileType.BINARY
                : ProcessedFileType.TEXT;
            try {
                if (type === ProcessedFileType.BINARY) {
                    processedFile = {
                        path: relativePath,
                        content: await file.arrayBuffer(),
                        type: ProcessedFileType.BINARY,
                    };
                } else {
                    processedFile = {
                        path: relativePath,
                        content: await file.text(),
                        type: ProcessedFileType.TEXT,
                    };
                }

                processedFiles.push(processedFile);
            } catch (error) {
                console.warn(`Error reading file ${file.name}:`, error);
            }
        }
        return processedFiles;
    };

    const processProjectFiles = async (fileList: FileList | File[]) => {
        setError('');
        setIsUploading(true);

        try {
            const files = Array.from(fileList);
            const fullPath = files[0]?.webkitRelativePath;

            // Normalize path by removing leading slash if present
            const normalizedPath = fullPath?.startsWith('/') ? fullPath.substring(1) : fullPath;
            const folderPath = normalizedPath?.substring(0, normalizedPath.indexOf('/'));

            const processedFiles = await filterAndProcessFiles(files);

            if (processedFiles.length === 0) {
                throw new Error('No valid files found in the selected folder');
            }

            const projectName = extractProjectName(processedFiles);

            if (!projectName) {
                setError('No project name found in the selected folder');
                return;
            }

            // Validate the project
            const validationResult = await validateNextJsProject(processedFiles);
            setValidation(validationResult);

            setProjectData({
                name: projectName,
                folderPath: folderPath,
                files: processedFiles,
                isLocal: false, // This is for sandbox uploads
                localPort: undefined,
            });
        } catch (error) {
            console.error('Error processing project:', error);
            setError(error instanceof Error ? error.message : 'Failed to process project');
        } finally {
            setIsUploading(false);
        }
    };

    const handleFileInputChange = useCallback(
        async (event: React.ChangeEvent<HTMLInputElement>) => {
            const files = event.target.files;
            if (files && files.length > 0) {
                if (projectMode === 'local') {
                    await processLocalProjectFiles(files);
                } else {
                    await processProjectFiles(files);
                }
            }
        },
        [projectMode, processLocalProjectFiles],
    );

    // Helper function to recursively read directory entries
    const readDirectory = async (dirEntry: any): Promise<File[]> => {
        const files: File[] = [];

        return new Promise((resolve) => {
            const reader = dirEntry.createReader();

            const readEntries = () => {
                reader.readEntries(async (entries: any[]) => {
                    if (entries.length === 0) {
                        resolve(files);
                        return;
                    }

                    for (const entry of entries) {
                        if (entry.isFile) {
                            const fileEntry = entry;
                            try {
                                let file = await new Promise<File>((resolve, reject) => {
                                    fileEntry.file(resolve, reject);
                                });

                                // Create a new File object with webkitRelativePath
                                const fileWithPath = new File([file], file.name, {
                                    type: file.type,
                                    lastModified: file.lastModified,
                                });
                                Object.defineProperty(fileWithPath, 'webkitRelativePath', {
                                    value: fileEntry.fullPath,
                                    writable: false,
                                    enumerable: true,
                                    configurable: false,
                                });

                                files.push(fileWithPath);
                            } catch (error) {
                                console.warn(`Error reading file ${entry.name}:`, error);
                            }
                        } else if (entry.isDirectory) {
                            const subFiles = await readDirectory(entry);
                            files.push(...subFiles);
                        }
                    }

                    // Continue reading entries (directories can have many entries)
                    readEntries();
                });
            };

            readEntries();
        });
    };

    const handleDrop = useCallback(async (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setIsDragging(false);

        const items = e.dataTransfer.items;
        const files: File[] = [];

        for (let i = 0; i < items.length; i++) {
            const item = items[i];

            if (item && item.kind === 'file') {
                const entry = item.webkitGetAsEntry();

                if (entry) {
                    if (entry.isFile) {
                        // Handle individual file
                        const fileEntry = entry as any;
                        try {
                            const file = await new Promise<File>((resolve, reject) => {
                                fileEntry.file(resolve, reject);
                            });
                            files.push(file);
                        } catch (error) {
                            console.warn(`Error reading file ${entry.name}:`, error);
                        }
                    } else if (entry.isDirectory) {
                        // Handle directory
                        const dirFiles = await readDirectory(entry);
                        files.push(...dirFiles);
                    }
                } else {
                    // Fallback for browsers that don't support webkitGetAsEntry
                    const file = item.getAsFile();
                    if (file) {
                        files.push(file);
                    }
                }
            }
        }

        if (files.length > 0) {
            if (projectMode === 'local') {
                await processLocalProjectFiles(files);
            } else {
                await processProjectFiles(files);
            }
        } else {
            setError('No files found in the dropped folder');
        }
    }, [projectMode, processLocalProjectFiles]);

    const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setIsDragging(true);
    }, []);

    const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        if (!e.currentTarget.contains(e.relatedTarget as Node)) {
            setIsDragging(false);
        }
    }, []);

    const reset = () => {
        resetProjectData();
        // Reset all related states
        setError('');
        setIsUploading(false);
        setIsDragging(false);
        setValidation(null);
        setProjectMode('upload');
        setLocalPort('3000');
        setLocalProjectPath('');
    };

    const renderHeader = () => {
        const headerConfig = {
            initial: {
                title: projectMode === 'local' ? 'Configure local development' : 'Select your project folder',
                description: projectMode === 'local' 
                    ? 'Set up Onlook to work with your local development server'
                    : "This is where we'll reference your App",
            },
            validating: {
                title: 'Verifying compatibility with Onlook',
                description: "We're checking to make sure this project can work with Onlook",
            },
            valid: {
                title: projectMode === 'local' ? 'Local project configured' : 'Project verified',
                description: projectMode === 'local' 
                    ? 'Your local development setup is ready'
                    : 'Your project is ready to import to Onlook',
            },
            invalid: {
                title: "This project won't work with Onlook",
                description: 'Onlook only works with NextJS + React + Tailwind projects',
            },
        };

        let config = headerConfig.initial;
        if (projectData.folderPath) {
            if (!validation) {
                config = headerConfig.validating;
            } else if (validation.isValid) {
                config = headerConfig.valid;
            } else {
                config = headerConfig.invalid;
            }
        }

        return (
            <>
                <CardTitle>{config.title}</CardTitle>
                <CardDescription>{config.description}</CardDescription>
            </>
        );
    };

    const renderProjectInfo = () => {
        if (!projectData.folderPath) {
            return (
                <motion.div
                    key="selectFolder"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    className="w-full space-y-4"
                >
                    <Tabs value={projectMode} onValueChange={(value) => setProjectMode(value as 'upload' | 'local')}>
                        <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="upload">Upload to Sandbox</TabsTrigger>
                            <TabsTrigger value="local">Local Development</TabsTrigger>
                        </TabsList>
                        <TabsContent value="upload" className="space-y-4">
                            <div
                                className={`
                                    w-full h-20 rounded-lg bg-gray-900 border border-gray rounded-lg m-0
                                    flex flex-col items-center justify-center gap-4
                                    duration-200 cursor-pointer
                                    ${isDragging
                                        ? 'border-blue-400 bg-blue-50'
                                        : 'border-gray-300 bg-gray-50 hover:bg-gray-700'
                                    }
                                    ${isUploading ? 'pointer-events-none opacity-50' : ''}
                                `}
                                onDrop={handleDrop}
                                onDragOver={handleDragOver}
                                onDragLeave={handleDragLeave}
                                onClick={handleClickUpload}
                            >
                                {isUploading ? (
                                    <div className="text-center">
                                        <div className="flex items-center justify-center gap-2">
                                            <Icons.LoadingSpinner className="w-4 h-4 text-gray-200 animate-spin" />
                                            <p className="text-sm font-medium text-gray-200">
                                                Uploading...
                                            </p>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="flex gap-3">
                                        <Icons.DirectoryOpen className="w-5 h-5 text-gray-200" />
                                        <p className="text-sm font-medium text-gray-200">
                                            Click to select your folder
                                        </p>
                                    </div>
                                )}
                            </div>
                        </TabsContent>
                        <TabsContent value="local" className="space-y-4">
                            <div className="space-y-4">
                                <div className="bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                                    <div className="flex items-start space-x-2">
                                        <Icons.InfoCircled className="w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5" />
                                        <div className="text-sm text-blue-800 dark:text-blue-200">
                                            <p className="font-medium mb-1">Local Development Mode</p>
                                            <p>This mode works directly with your local files. Make sure your dev server is running on the specified port.</p>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <label className="text-sm font-medium">Local Port</label>
                                    <input
                                        type="number"
                                        value={localPort}
                                        onChange={(e) => setLocalPort(e.target.value)}
                                        className="w-full mt-1 px-3 py-2 bg-gray-800 border border-gray-700 rounded-md"
                                        placeholder="3000"
                                    />
                                    <p className="text-xs text-gray-400 mt-1">Port where your development server is running</p>
                                </div>
                                <div>
                                    <label className="text-sm font-medium">Project Path</label>
                                    <div className="relative">
                                        <input
                                            type="text"
                                            value={localProjectPath}
                                            onChange={(e) => setLocalProjectPath(e.target.value)}
                                            className="w-full mt-1 px-3 py-2 bg-gray-800 border border-gray-700 rounded-md"
                                            placeholder="/Users/<USER>/Documents/my-project"
                                        />
                                        <Button
                                            type="button"
                                            onClick={() => setLocalProjectPath('/Users/<USER>/Documents/my-project')}
                                            variant="ghost"
                                            size="sm"
                                            className="absolute right-1 top-1 text-xs"
                                        >
                                            Template
                                        </Button>
                                    </div>
                                    <p className="text-xs text-gray-400 mt-1">
                                        Absolute path to your project directory. Auto-filled with a template when you select a folder - replace [username] with your actual username.
                                        {localProjectPath && localProjectPath.includes('[username]') && (
                                            <span className="text-amber-400 block mt-1">⚠ Please replace [username] with your actual username</span>
                                        )}
                                    </p>
                                </div>
                                <div
                                    className={`
                                        w-full h-20 rounded-lg bg-gray-900 border border-gray rounded-lg m-0
                                        flex flex-col items-center justify-center gap-4
                                        duration-200 cursor-pointer hover:bg-gray-700
                                    `}
                                    onClick={handleClickUpload}
                                >
                                    <div className="flex gap-3">
                                        <Icons.DirectoryOpen className="w-5 h-5 text-gray-200" />
                                        <p className="text-sm font-medium text-gray-200">
                                            Select your project folder to auto-detect path
                                        </p>
                                    </div>
                                </div>
                                <p className="text-xs text-gray-400">This helps auto-detect the project path. Files won't be uploaded in local mode.</p>
                            </div>
                        </TabsContent>
                    </Tabs>

                    <input
                        ref={fileInputRef}
                        type="file"
                        style={{ display: 'none' }}
                        onChange={handleFileInputChange}
                        directory=""
                        webkitdirectory=""
                    />
                </motion.div>
            );
        }

        const statusConfig = {
            valid: {
                bgColor: 'bg-teal-900',
                borderColor: 'border-teal-600',
                iconBgColor: 'bg-teal-500',
                textColor: 'text-teal-100',
                subTextColor: 'text-teal-200',
                icon: (
                    <Icons.CheckCircled className="w-5 h-5 text-teal-200 group-hover:opacity-0 transition-opacity duration-200" />
                ),
                showError: false,
            },
            invalid: {
                bgColor: 'bg-amber-900',
                borderColor: 'border-amber-600',
                iconBgColor: 'bg-amber-500',
                textColor: 'text-amber-100',
                subTextColor: 'text-amber-200',
                icon: <Icons.ExclamationTriangle className="w-5 h-5 text-amber-200" />,
                showError: true,
            },
        };

        const config = validation?.isValid ? statusConfig.valid : statusConfig.invalid;

        return (
            <motion.div
                key="folderPath"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className={`w-full flex flex-row items-center border p-4 rounded-lg ${config.bgColor} ${config.borderColor} gap-2 group relative`}
            >
                <div
                    className={`flex flex-col gap-2 w-full ${config.showError ? '' : 'flex-row items-center justify-between'}`}
                >
                    <div className="flex flex-row items-center justify-between w-full gap-3">
                        <div className={`p-3 ${config.iconBgColor} rounded-lg`}>
                            <Icons.Directory className="w-5 h-5" />
                        </div>
                        <div className="flex flex-col gap-1 break-all w-full">
                            <p className={`text-regular ${config.textColor}`}>
                                {projectData.name}
                                {projectData.isLocal && (
                                    <span className="ml-2 px-2 py-1 text-xs bg-blue-500 text-white rounded">
                                        Local
                                    </span>
                                )}
                            </p>
                            <p className={`text-mini ${config.subTextColor}`}>
                                {projectData.isLocal 
                                    ? `Local development (Port: ${projectData.localPort})` 
                                    : projectData.folderPath
                                }
                            </p>
                        </div>
                        {config.icon}
                    </div>
                    {config.showError && (
                        <p className={`${config.textColor} text-sm`}>
                            This is not a NextJS Project
                        </p>
                    )}
                </div>
                {validation?.isValid && (
                    <Button
                        className={`absolute right-4 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 hover:bg-transparent p-0 size-10 transition-opacity duration-200 ${config.bgColor}`}
                        variant="ghost"
                        size="icon"
                        onClick={reset}
                    >
                        <Icons.MinusCircled className="w-5 h-5 text-teal-200" />
                    </Button>
                )}
            </motion.div>
        );
    };

    return (
        <>
            <StepHeader>{renderHeader()}</StepHeader>
            <StepContent>{renderProjectInfo()}</StepContent>
            <StepFooter>
                <Button type="button" onClick={prevStep} variant="outline" className="px-3 py-2">
                    Cancel
                </Button>
                {projectData.folderPath ? (
                    <Button
                        type="button"
                        onClick={validation?.isValid ? nextStep : reset}
                        className="px-3 py-2"
                        disabled={isUploading}
                    >
                        {validation?.isValid ? 'Finish setup' : 'Select a different folder'}
                    </Button>
                ) : (
                    <Button
                        disabled={!!error || isUploading || (projectMode === 'local' && (!localPort || !localProjectPath || localProjectPath.includes('[username]')))}
                        type="button"
                        onClick={projectMode === 'local' ? handleLocalContinue : nextStep}
                        className="px-3 py-2"
                    >
                        Continue
                    </Button>
                )}
            </StepFooter>
        </>
    );
};
