import { SystemTheme } from '@onlook/models/assets';
import { Button } from '@onlook/ui/button';
import { Icons } from '@onlook/ui/icons';
import { toast } from '@onlook/ui/sonner';
import { Tooltip, TooltipContent, TooltipTrigger } from '@onlook/ui/tooltip';
import { useEffect, useState } from 'react';

export function ThemeGroup({ frameData }: { frameData: any }) {
    const [theme, setTheme] = useState<SystemTheme>(SystemTheme.SYSTEM);
    const [isFrameReady, setIsFrameReady] = useState(false);
    
    useEffect(() => {
        let pollInterval: NodeJS.Timeout | null = null;
        let timeoutId: NodeJS.Timeout | null = null;
        
        const checkFrameAndGetTheme = async () => {
            if (!frameData?.view) {
                setIsFrameReady(false);
                return false;
            }
            
            try {
                const currentTheme = await frameData.view.getTheme();
                setTheme(currentTheme);
                setIsFrameReady(true);
                return true;
            } catch (error) {
                console.debug('Frame view not ready yet, will retry...');
                setIsFrameReady(false);
                return false;
            }
        };
        
        // Try immediately
        checkFrameAndGetTheme().then((success) => {
            if (!success) {
                // If not successful, poll every 500ms until frame is ready
                pollInterval = setInterval(async () => {
                    const success = await checkFrameAndGetTheme();
                    if (success && pollInterval) {
                        clearInterval(pollInterval);
                        pollInterval = null;
                    }
                }, 500);
                
                // Stop polling after 30 seconds to avoid infinite polling
                timeoutId = setTimeout(() => {
                    if (pollInterval) {
                        clearInterval(pollInterval);
                        pollInterval = null;
                        console.warn('ThemeGroup: stopped polling for frame readiness after 30 seconds');
                    }
                }, 30000);
            }
        });
        
        // Cleanup polling on unmount or when frameData changes
        return () => {
            if (pollInterval) {
                clearInterval(pollInterval);
            }
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
        };
    }, [frameData]);

    async function changeTheme(newTheme: SystemTheme) {
        if (!frameData?.view) {
            toast.error('Cannot change theme: frame not ready');
            return;
        }
        
        const previousTheme = theme;
        setTheme(newTheme);
        
        try {
            const success = await frameData.view.setTheme(newTheme);
            if (!success) {
                toast.error('Failed to change theme');
                setTheme(previousTheme);
            }
        } catch (error) {
            console.error('Error changing theme:', error);
            toast.error('Failed to change theme');
            setTheme(previousTheme);
        }
    }

    return (
        <>
            <Tooltip key="system">
                <TooltipTrigger asChild>
                    <Button
                        variant="ghost"
                        size="icon"
                        disabled={!isFrameReady}
                        className={`h-8 w-8 ${theme === SystemTheme.SYSTEM ? 'bg-background-tertiary hover:bg-background-tertiary' : 'hover:bg-background-tertiary/50 text-foreground-onlook'}`}
                        onClick={() => changeTheme(SystemTheme.SYSTEM)}
                    >
                        <Icons.Laptop className="h-4 w-4" />
                    </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                    {isFrameReady ? 'System Theme' : 'System Theme (Frame loading...)'}
                </TooltipContent>
            </Tooltip>
            <Tooltip key="dark">
                <TooltipTrigger asChild>
                    <Button
                        variant="ghost"
                        size="icon"
                        disabled={!isFrameReady}
                        className={`h-8 w-8 ${theme === SystemTheme.DARK ? 'bg-background-tertiary hover:bg-background-tertiary' : 'hover:bg-background-tertiary/50 text-foreground-onlook'}`}
                        onClick={() => changeTheme(SystemTheme.DARK)}
                    >
                        <Icons.Moon className="h-4 w-4" />
                    </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                    {isFrameReady ? 'Dark Theme' : 'Dark Theme (Frame loading...)'}
                </TooltipContent>
            </Tooltip>
            <Tooltip key="light">
                <TooltipTrigger asChild>
                    <Button
                        variant="ghost"
                        size="icon"
                        disabled={!isFrameReady}
                        className={`h-8 w-8 ${theme === SystemTheme.LIGHT ? 'bg-background-tertiary hover:bg-background-tertiary' : 'hover:bg-background-tertiary/50 text-foreground-onlook'}`}
                        onClick={() => changeTheme(SystemTheme.LIGHT)}
                    >
                        <Icons.Sun className="h-4 w-4" />
                    </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                    {isFrameReady ? 'Light Theme' : 'Light Theme (Frame loading...)'}
                </TooltipContent>
            </Tooltip>
        </>
    );
} 